package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.AssignFeelingUseCase;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.repository.ConversationRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;
    lombok.RequiredArgsConstructor;
    org.springframework.stereotype.Service;
    reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AssignFeelingService implements AssignFeelingUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> assignFeeling(ConversationDomainId conversationId, FeelingDomain feeling) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.assignFeeling(feeling);
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}