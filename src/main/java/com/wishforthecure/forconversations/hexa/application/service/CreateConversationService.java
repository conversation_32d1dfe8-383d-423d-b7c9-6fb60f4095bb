package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.application.port.in.CreateConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.service.SourceParsingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;
import java.util.HashSet;

@Service
@RequiredArgsConstructor
public class CreateConversationService implements CreateConversationUseCase {

    private final SourceRepository sourceRepository;
    private final SourceParsingService parsingService;
    private final ConversationRepository conversationRepository;

    @Override
    public Mono<ConversationId> create(CreateConversationCommand command) {
        return sourceRepository.findById(command.getSourceId())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Source not found")))
                .flatMap(source -> {
                    List<Message> messages = parsingService.parse(source, command.getSourceType());
                    Conversation conversation = new Conversation(
                            new ConversationId(UUID.randomUUID()),
                            command.getConversationName(),
                            messages.stream().map(Message::getTime).min(Instant::compareTo).orElse(Instant.now()),
                            messages.stream().map(Message::getTime).max(Instant::compareTo).orElse(Instant.now()),
                            null,
                            new HashSet<>(),
                            messages
                    );
                    return conversationRepository.save(conversation).thenReturn(conversation.getId());
                });
    }
}

path: src/application/service/AddTagService.java
