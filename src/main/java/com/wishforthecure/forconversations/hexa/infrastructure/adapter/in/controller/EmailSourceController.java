package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller;

import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import reactor.core.publisher.Mono;

public class EmailSourceController {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceController.class);
    private final EmailSourcePort emailSourcePort;

    public EmailSourceController(EmailSourcePort emailSourcePort) {
        this.emailSourcePort = emailSourcePort;
    }

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        LOG.info("REST request to initiate chat data upload");
        return emailSourcePort.upload(emailSourceUploadDTO);
    }

    @PostMapping(value = "/save")
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        LOG.info("REST request to initiate chat data upload");
        return emailSourcePort.save(emailSourceSaveDTO);
    }
}

path: src/infrastructure/adapter/in/controller/WhatsAppSourceController.java
