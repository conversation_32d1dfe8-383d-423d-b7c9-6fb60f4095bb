package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import java.util.List;
import lombok.Data;

@Data
public class EmailSourceUploadDTO {

    private byte[] file;
    private List<MessageFilter> filterList;
}

path: src/infrastructure/adapter/in/controller/dto/EmailSourceSaveDTO.java
