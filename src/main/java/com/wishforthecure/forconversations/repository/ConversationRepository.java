package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;

import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Spring Data MongoDB reactive repository for the Conversation entity.
 */
@SuppressWarnings("unused")
@Repository
public interface ConversationRepository extends ReactiveMongoRepository<Conversation, String> {
    Flux<Conversation> findByUserId(String userId);

    Flux<Conversation> findByUserIdOrderByStartDesc(String userId);

    Mono<Void> findById(ConversationDomainId conversationDomainId);
}
